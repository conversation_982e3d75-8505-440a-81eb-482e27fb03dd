pipeline {
    agent any
    stages {
        //   Étape 1:Préparation de l'environnement 
        stage('Préparation environnement') {
            steps {
                echo "Création des répertoires et configuration des permissions"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            mkdir -p /var/www/html/hi3d/{backend,frontend}
                            mkdir -p /var/www/html/hi3d/backend/bootstrap/cache
                            mkdir -p /var/www/html/hi3d/backend/storage
                            chmod -R 775 /var/www/html/hi3d/backend/bootstrap/cache
                            chmod -R 775 /var/www/html/hi3d/backend/storage
                            chown -R www-data:www-data /var/www/html/hi3d/backend
                            chmod -R 775 /var/www/html/hi3d/frontend
                            chown -R www-data:www-data /var/www/html/hi3d/frontend
                        """,
                        remoteDirectory: '/var/www/html/hi3d/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 2: Vérification des scripts et permissions
        stage('Set execute permissions') {
            steps {
                echo "Vérification et ajout des droits d'exécution"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            if [ ! -f /var/www/html/hi3d/jenkins-pipeline/removerepertoryback.sh ]; then
                                echo "Fichier removerepertoryback.sh non trouvé"
                                exit 1
                            fi
                            chmod +x /var/www/html/hi3d/jenkins-pipeline/*.sh
                        """,
                        remoteDirectory: '/var/www/html/hi3d/jenkins-pipeline/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 3: Déploiement du code
        stage('Deployer Wassim Projet') {
            steps {
                echo "Déploiement du projet Wassim en cours..."
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        sourceFiles: '**/*',
                        remoteDirectory: '/var/www/html/hi3d/',
                        cleanRemote: false,
                        execCommand: '',
                        execTimeout: 120000
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: false
                )])
            }
        }

        // Étape 4: Préparation Laravel
        stage('Préparation Laravel') {
            steps {
                echo "Configuration des répertoires Laravel"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            mkdir -p /var/www/html/hi3d/backend/bootstrap/cache
                            mkdir -p /var/www/html/hi3d/backend/storage/{app,framework,logs}
                            mkdir -p /var/www/html/hi3d/backend/storage/framework/{cache,sessions,views}
                            chmod -R 775 /var/www/html/hi3d/backend/bootstrap/cache
                            chmod -R 775 /var/www/html/hi3d/backend/storage
                            chown -R www-data:www-data /var/www/html/hi3d/backend
                        """,
                        remoteDirectory: '/var/www/html/hi3d/backend',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 5: Mise à jour des dépendances Backend
        stage('Composer Update') {
            steps {
                echo "Exécution de 'Composer Update'..."
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            export COMPOSER_ALLOW_SUPERUSER=1
                            cd /var/www/html/hi3d/backend
                            composer install --no-dev --optimize-autoloader
                            php artisan cache:clear
                            php artisan config:clear
                            php artisan view:clear
                        """,
                        remoteDirectory: '/var/www/html/hi3d/backend',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 6: Nettoyage Frontend
        stage('Supprimer Node Modules et Build') {
            steps {
                echo "Suppression de node_modules et build"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            cd /var/www/html/hi3d/frontend
                            rm -rf node_modules
                            rm -rf build
                        """,
                        remoteDirectory: '/var/www/html/hi3d/frontend/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 7: Installation et build Frontend
        stage('Npm install et npm run build') {
            steps {
                echo "Installation des dépendances et build Frontend"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            cd /var/www/html/hi3d/frontend
                            npm ci --silent
                            npm audit fix
                            npm run build
                        """,
                        remoteDirectory: '/var/www/html/hi3d/frontend/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 8: Exécution des migrations
        // stage('Run migration') {
        //     steps {
        //         echo "Exécution des migrations"
        //         sshPublisher(publishers: [sshPublisherDesc(
        //             configName: 'PROJET WASSIM',
        //             transfers: [sshTransfer(
        //                 execCommand: """
        //                     cd /var/www/html/hi3d/backend
        //                     export COMPOSER_ALLOW_SUPERUSER=1
        //                     php artisan migrate --force
        //                 """,
        //                 remoteDirectory: '/var/www/html/hi3d/backend',
        //                 cleanRemote: false,
        //                 sourceFiles: ''
        //             )],
        //             usePromotionTimestamp: false,
        //             useWorkspaceInPromotion: false,
        //             verbose: true
        //         )])
        //     }
        // }

        // Étape 9: Redémarrage des services
        stage('Restart nginx et pm2') {
            steps {
                echo "Redémarrage de nginx et pm2"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            systemctl restart nginx
                            cd /var/www/html/hi3d/frontend
                            pm2 restart hi-3d-frontend
                            pm2 save
                        """,
                        remoteDirectory: '/var/www/html/hi3d/frontend/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

        // Étape 10: Finalisation des permissions
        stage('Regiving autorisation final') {
            steps {
                echo "Réattribution des permissions finales"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            chown -R www-data:www-data /var/www/html/hi3d
                            chmod -R 775 /var/www/html/hi3d
                            chmod -R 775 /var/www/html/hi3d/backend/storage
                            chmod -R 775 /var/www/html/hi3d/backend/bootstrap/cache
                            find /var/www/html/hi3d/backend -type f -exec chmod 664 {} \\;
                        """,
                        remoteDirectory: '/var/www/html/hi3d/',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }

                // Étape 8: Exécution des migrations
        stage('Créer la table client_profiles professional_profiles freelance_profiles client_profiles') {
            steps {
                echo "Exécution des migrations"
                sshPublisher(publishers: [sshPublisherDesc(
                    configName: 'PROJET WASSIM',
                    transfers: [sshTransfer(
                        execCommand: """
                            cd /var/www/html/hi3d/backend
                            export COMPOSER_ALLOW_SUPERUSER=1
                            php artisan migrate --force
                            php artisan migrate:fresh --path=database/migrations/2025_05_01_132217_create_client_profiles_table_final.php
                            php artisan migrate:refresh --path=database/migrations/2025_02_17_065534_create_professional_profiles_table.php
                            php artisan migrate:refresh --path=database/migrations/2025_02_23_155900_create_freelance_profiles_table.php
                            php artisan migrate:refresh --path=database/migrations/2025_05_01_132217_create_client_profiles_table_final.php
                        """,
                        remoteDirectory: '/var/www/html/hi3d/backend',
                        cleanRemote: false,
                        sourceFiles: ''
                    )],
                    usePromotionTimestamp: false,
                    useWorkspaceInPromotion: false,
                    verbose: true
                )])
            }
        }
    }

    post {
        always {
            echo "Construction terminée - Vérifiez les logs pour plus de détails"
        }
        success {
            echo "Déploiement réussi!"
        }
        failure {
            echo "Échec du déploiement - Veuillez corriger les erreurs"
        }
    }
}