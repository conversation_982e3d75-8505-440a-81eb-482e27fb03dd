# Tests Automatisés du Workflow

## Vue d'ensemble

Ce document décrit les tests automatisés créés pour valider la séquence complète du workflow des offres, basée sur la séquence documentée dans `quick-start-testing.md`.

## Séquence testée

La séquence automatisée teste le workflow complet suivant :

1. **Authentification**
   - Login Client → Token sauvegardé automatiquement
   - Login Professional → Token sauvegardé automatiquement

2. **Création d'offre**
   - Create Open Offer → offer_id sauvegardé automatiquement

3. **Candidature**
   - Apply to Offer → application_id sauvegardé automatiquement

4. **Gestion candidature**
   - Accept Application → Candidature acceptée
   - View Accepted Applications → Vérifier la liste

5. **Attribution finale**
   - Assign Offer to Professional → Offre attribuée

## Fichiers de test créés

### 1. `AutomatedWorkflowSequenceTest.php`

Test PHPUnit complet et détaillé avec méthodes séparées pour chaque étape.

**Caractéristiques :**
- Méthodes privées pour chaque étape
- Vérifications détaillées à chaque étape
- Gestion complète des erreurs
- Vérifications finales approfondies

**Exécution :**
```bash
php artisan test --filter AutomatedWorkflowSequenceTest
```

### 2. `QuickStartTestingSequenceTest.php`

Test PHPUnit simplifié qui reproduit exactement la séquence du guide.

**Caractéristiques :**
- Structure simple et claire
- Suit exactement la séquence documentée
- Affichage des résultats en console
- Test de répétabilité inclus

**Exécution :**
```bash
php artisan test --filter QuickStartTestingSequenceTest
```

### 3. `run-automated-sequence-test.php`

Script PHP standalone pour tester l'API directement via HTTP.

**Caractéristiques :**
- Exécution indépendante de Laravel
- Requêtes HTTP directes
- Affichage en temps réel du progrès
- Résumé détaillé des résultats

**Exécution :**
```bash
php run-automated-sequence-test.php
```

## Configuration requise

### Prérequis

1. **Base de données de test configurée**
2. **Utilisateurs de test créés** (ou utilisation des factories)
3. **API accessible** (pour le script standalone)

### Variables d'environnement

Pour le script standalone, ajustez ces variables dans le fichier :

```php
$baseUrl = 'http://localhost:8000/api'; // URL de votre API
$clientEmail = '<EMAIL>';
$professionalEmail = '<EMAIL>';
$password = 'password123';
```

## Utilisation

### Tests PHPUnit

```bash
# Exécuter tous les tests de workflow
php artisan test tests/Feature/AutomatedWorkflowSequenceTest.php
php artisan test tests/Feature/QuickStartTestingSequenceTest.php

# Exécuter avec verbosité
php artisan test --filter AutomatedWorkflowSequenceTest -v

# Exécuter en mode debug
php artisan test --filter QuickStartTestingSequenceTest --debug
```

### Script standalone

```bash
# Exécution directe
php run-automated-sequence-test.php

# Avec redirection de sortie
php run-automated-sequence-test.php > test-results.log 2>&1
```

## Résultats attendus

### Succès

Lorsque tous les tests passent, vous devriez voir :

```
✅ Séquence de test automatisée terminée avec succès !
📊 Résultats :
   - Client Token: 1|abc123...
   - Professional Token: 2|def456...
   - Offer ID: 1
   - Application ID: 1
   - Statut final de l'offre: in_progress
   - Statut final de la candidature: accepted
```

### Vérifications en base de données

Après exécution réussie, vérifiez :

```sql
-- Offre créée et en cours
SELECT * FROM open_offers WHERE status = 'in_progress';

-- Candidature acceptée
SELECT * FROM offer_applications WHERE status = 'accepted';

-- Tokens d'authentification
SELECT * FROM personal_access_tokens WHERE name = 'api-token';
```

## Dépannage

### Erreurs courantes

1. **Erreur 401 - Non authentifié**
   - Vérifiez que les utilisateurs existent
   - Vérifiez que les emails sont vérifiés
   - Vérifiez les mots de passe

2. **Erreur 422 - Validation**
   - Vérifiez les données envoyées
   - Vérifiez les règles de validation
   - Vérifiez les champs requis

3. **Erreur 404 - Non trouvé**
   - Vérifiez les routes API
   - Vérifiez que l'API est démarrée
   - Vérifiez les IDs générés

### Debug

Pour déboguer les tests :

```bash
# Activer le debug Laravel
php artisan test --filter AutomatedWorkflowSequenceTest --debug

# Voir les logs
tail -f storage/logs/laravel.log

# Vérifier la base de données
php artisan tinker
>>> DB::table('open_offers')->get();
>>> DB::table('offer_applications')->get();
```

## Intégration CI/CD

### GitHub Actions

```yaml
name: Automated Workflow Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install dependencies
        run: composer install
      - name: Run automated workflow tests
        run: |
          php artisan test --filter AutomatedWorkflowSequenceTest
          php artisan test --filter QuickStartTestingSequenceTest
```

### Jenkins

```groovy
pipeline {
    agent any
    stages {
        stage('Test Automated Workflow') {
            steps {
                sh 'php artisan test tests/Feature/AutomatedWorkflowSequenceTest.php'
                sh 'php artisan test tests/Feature/QuickStartTestingSequenceTest.php'
            }
        }
    }
}
```

## Maintenance

### Mise à jour des tests

Lorsque l'API évolue, mettez à jour :

1. **Données de test** dans les méthodes `setup`
2. **Assertions** selon les nouvelles réponses
3. **Routes** si elles changent
4. **Validation** selon les nouvelles règles

### Ajout de nouvelles étapes

Pour ajouter une étape à la séquence :

1. Ajoutez la méthode dans les tests PHPUnit
2. Ajoutez la fonction dans le script standalone
3. Mettez à jour la documentation
4. Testez la nouvelle séquence complète

## Bonnes pratiques

1. **Isolation des tests** - Chaque test doit être indépendant
2. **Nettoyage** - Utilisez `RefreshDatabase` pour les tests PHPUnit
3. **Assertions claires** - Vérifiez les résultats attendus
4. **Gestion d'erreurs** - Testez aussi les cas d'échec
5. **Documentation** - Maintenez cette documentation à jour
