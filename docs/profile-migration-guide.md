# Profile System Migration Guide

This guide provides instructions for migrating from the old profile system to the new unified profile structure.

## Overview

We've restructured the profile system to simplify and consolidate multiple profile types into a single, extensible structure. This guide will help you transition your code to use the new structure.

## Database Changes

The new structure consists of:

1. **User Table** (unchanged)
   - Basic authentication info (email, password)
   - Common fields (first_name, last_name)
   - The `is_professional` flag to distinguish user types

2. **Profile Table** (new consolidated table)
   - Common profile fields for all users
   - One-to-one relationship with User
   - Fields: user_id, phone, address, city, country, bio, avatar, social_links, completion_percentage

3. **ProfessionalDetail Table**
   - Professional-specific fields only
   - One-to-one relationship with Profile
   - Fields: profession, expertise, years_of_experience, hourly_rate, availability_status, skills, portfolio, etc.

4. **ClientDetail Table**
   - Client-specific fields only
   - One-to-one relationship with Profile
   - Fields: type (particulier/entreprise), company_name, industry, company_size, etc.

## Migration Steps

### 1. Run Migrations

Run the new migrations to create the new tables and migrate data:

```bash
php artisan migrate
```

This will:
- Create the new tables
- Migrate data from old tables to new ones
- Keep the old tables intact for now

### 2. Update Backend Code

#### Using the New Profile Service

Replace old profile-related code with the new ProfileService:

```php
// Old way
$professionalProfile = $user->professionalProfile;
$clientProfile = $user->clientProfile;

// New way
$profileService = app(App\Services\ProfileService::class);
$profile = $profileService->getProfileForUser($user);
```

#### Accessing Profile Data

```php
// Old way - Professional
$skills = $user->professionalProfile->skills;
$hourlyRate = $user->professionalProfile->hourly_rate;

// Old way - Client
$companyName = $user->clientProfile->company_name;

// New way - Professional
$skills = $user->profile->professionalDetails->skills;
$hourlyRate = $user->profile->professionalDetails->hourly_rate;

// New way - Client
$companyName = $user->profile->clientDetails->company_name;
```

#### Updating Profile Data

```php
// Old way
$user->professionalProfile->update([
    'skills' => $skills,
    'hourly_rate' => $hourlyRate
]);

// New way
$profileService->updateProfile($user, [
    'skills' => $skills,
    'hourly_rate' => $hourlyRate
]);
```

### 3. Update Frontend Code

#### Using the New Profile Service

Replace old profile service with the new unified profile service:

```typescript
// Old way
import { profileService } from '../services/profileService';
const { profile } = await profileService.getProfile();

// New way
import { newProfileService } from '../services/newProfileService';
const { profile } = await newProfileService.getProfile();
```

#### Using the Profile Context

```tsx
// Old way
import { useProfile } from '../components/ProfileContext';
const { profile, updateProfile } = useProfile();

// New way
import { useUnifiedProfile } from '../context/UnifiedProfileContext';
const { profile, updateProfile } = useUnifiedProfile();
```

#### Accessing Profile Data

```tsx
// Old way - Professional
const skills = profile.skills;
const hourlyRate = profile.hourly_rate;

// Old way - Client
const companyName = profile.company_name;

// New way - All profiles have common fields directly accessible
const phone = profile.phone;
const bio = profile.bio;

// New way - Professional-specific fields
const skills = profile.skills;
const hourlyRate = profile.hourly_rate;

// New way - Client-specific fields
const companyName = profile.company_name;
```

### 4. Testing

Test both the old and new profile systems in parallel:

- Old profile routes: `/dashboard/profile` and `/dashboard/profile/edit`
- New profile routes: `/dashboard/profile-new` and `/dashboard/profile-new/edit`

Once you've verified that the new system works correctly, you can switch to using it exclusively.

### 5. Final Migration

After thorough testing, run the final migration to remove the old tables:

```bash
php artisan migrate
```

This will drop the old profile tables:
- `professional_profiles`
- `freelance_profiles`
- `client_profiles`
- `company_profiles`

## API Endpoints

### Old Endpoints (to be deprecated)

- `GET /api/profile` - Get the profile of the authenticated user
- `PUT /api/profile` - Update the profile of the authenticated user
- `POST /api/profile/complete-profile` - Complete the profile (first login)
- `GET /api/profile/completion` - Get the profile completion status
- `POST /api/profile/avatar` - Upload avatar
- `POST /api/profile/portfolio` - Upload portfolio items
- `DELETE /api/profile/portfolio/{id}` - Delete a portfolio item
- `PUT /api/profile/availability` - Update availability status

### New Endpoints

- `GET /api/profile/new` - Get the profile of the authenticated user
- `PUT /api/profile/new` - Update the profile of the authenticated user
- `POST /api/profile/new/complete` - Complete the profile (first login)
- `GET /api/profile/new/completion` - Get the profile completion status
- `POST /api/profile/new/avatar` - Upload avatar
- `POST /api/profile/new/portfolio` - Upload portfolio items
- `DELETE /api/profile/new/portfolio/{id}` - Delete a portfolio item
- `PUT /api/profile/new/availability` - Update availability status

## Benefits of the New Structure

- **Reduced Redundancy**: Common fields are stored in a single place
- **Simplified Queries**: Easier to retrieve and update profile data
- **Better Maintainability**: Changes to common fields only need to be made in one place
- **Extensibility**: Easier to add new profile types or fields in the future
- **Cleaner Code**: More logical organization of profile-related functionality

## Need Help?

If you encounter any issues during migration, please refer to the detailed documentation in `backend/docs/profile-structure.md` or contact the development team.
