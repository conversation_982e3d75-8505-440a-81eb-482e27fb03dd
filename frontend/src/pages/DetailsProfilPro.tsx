// import Header from '@/components/Header';
import ProfileSection from '../components/ProfileSection';
// import Footer from '@/components/Footer';
import Header from '../components/Header';
import Footer from '../components/Footer';
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const DetailsProfilPro = () => {
    const { id } = useParams<{ id: string }>();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      {id && <ProfileSection id={id} />}
      <Footer />
    </div>
  );
};

export default DetailsProfilPro;