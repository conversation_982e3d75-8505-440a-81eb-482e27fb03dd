import React from 'react';
import Header from './Header';
import Gallery from './Gallery';
import Footer from './Footer';

const FavoriteTitle: React.FC = () => (
  <div className="container mx-auto px-4 pt-12 pb-4 mb-10">
    <h1 className="text-5xl font-bold text-gray-900 mb-2 py-5">My Favorite</h1>
    <div className="border-b  border-gray-300 w-full" />
  </div>
);

const Favorite: React.FC = () => {
  const galleryItems = [
    { 
      id: 1, 
      title: 'SIRIUS - Space Station', 
      author: '<PERSON>', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/44.jpg', 
      isPro: true, 
      likes: 35, 
      views: '3.3k', 
      image: 'https://cdnb.artstation.com/p/assets/covers/images/089/146/321/smaller_square/samuel-navarro-samuel-navarro-32.jpg?1750169043'
    },
    { 
      id: 2, 
      title: 'Purple Lake Environment', 
      author: '<PERSON><PERSON>', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/32.jpg', 
      isPro: false, 
      likes: 83, 
      views: '18.9k', 
      image: 'https://cdnb.artstation.com/p/assets/covers/images/067/372/527/smaller_square/dmitry-fedorov-dmitry-fedorov-icon-new.jpg?1695215260'
    },
    { 
      id: 3, 
      title: 'Cyberpunk Cityscape', 
      author: 'Maria Rodriguez', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/28.jpg', 
      isPro: true, 
      likes: 127, 
      views: '45.2k', 
      image: 'https://cdna.artstation.com/p/assets/covers/images/059/474/796/20230215081009/smaller_square/flycat-fly-flycat-fly-render-5g.jpg?1676470209'
    },
    { 
      id: 4, 
      title: 'Fantasy Character Design', 
      author: 'Alex Chen', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/67.jpg', 
      isPro: true, 
      likes: 94, 
      views: '22.1k', 
      image: 'https://cdna.artstation.com/p/assets/covers/images/074/103/832/smaller_square/francois-larrieu-francois-larrieu-thumbnail.jpg?1711233634'
    },
    { 
      id: 5, 
      title: 'Modern Architecture Villa', 
      author: 'Sophie Dubois', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/55.jpg', 
      isPro: false, 
      likes: 156, 
      views: '67.8k', 
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop'
    },
    { 
      id: 6, 
      title: 'Sci-Fi Robot Animation', 
      author: 'David Kim', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/89.jpg', 
      isPro: true, 
      likes: 203, 
      views: '89.4k', 
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop'
    },
    { 
      id: 7, 
      title: 'Nature Landscape Render', 
      author: 'Emma Wilson', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/33.jpg', 
      isPro: false, 
      likes: 67, 
      views: '15.6k', 
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
    }
  ];
  return (
    <>
      <Header />
      <div className="w-full text-center my-8">
        <h2
          style={{
            fontSize: '3rem',
            fontFamily: 'Arial, sans-serif',
            letterSpacing: '0.06em',
            paddingTop: '32px',
            paddingBottom: '32px',
            fontWeight: 600,
            lineHeight: 1.1,
            color: '#222',
          }}
        >
          Save your favorite<br />3d Artiste for later
        </h2>
      </div>
      <div className="w-full max-w-[1512px] mx-auto px-4 md:px-20">
        <FavoriteTitle />
        <Gallery items={galleryItems} marginBottom={350} />
      </div>
      <Footer />
    </>
  );
};

export default Favorite; 