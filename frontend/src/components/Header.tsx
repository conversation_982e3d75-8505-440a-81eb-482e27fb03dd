import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, User, Menu, Globe, Settings, BarChart, LogOut, Bell } from 'lucide-react';
import ProfileWizard from './ProfileWizard';
import Button from './ui/Button';
import Avatar from './ui/Avatar';
import Dropdown from './ui/Dropdown';
import MobileMenu from './ui/MobileMenu';
import Container from './layout/Container';
import NotificationDropdown from './notifications/NotificationDropdown';
import { useNotifications } from './notifications/NotificationContext';

// Define types for navigation links
interface NavLink {
  label: string;
  href: string;
  children?: Array<{ label: string; href: string }>;
}

function Header() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Get notifications from context
  const { notifications, markAsRead, markAllAsRead, loading, error } = useNotifications();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  
  // Afficher les infos user dans la console
  console.log('User object:', user);
  console.log('user.is_professional:', user.is_professional);
  
  // Check if user is authenticated (has token and user data)
  const isAuthenticated = !!token && !!user && !!user.id;
  const navigate = useNavigate();

  // Navigation links (ENGLISH, as per image)
  const navLinks: NavLink[] = [
    { label: 'Explorer', href: '/explore' },
    { label: 'Blog', href: '/blog' },
    { label: '???', href: '#' },
  ];

  // User dropdown items
  const userDropdownItems = [
    {
      label: `${user.first_name || ''} ${user.last_name || ''}`,
      href: '/dashboard/profile',
      divider: true
    },
    {
      label: 'Tableau de bord',
      href: '/dashboard',
      icon: <BarChart className="w-4 h-4" />
    },
    {
      label: 'Mon profil',
      href: user.is_professional !== false ? '/dashboard/profile' : '/dashboard/client-profile',
      icon: <User className="w-4 h-4" />
    },
    {
      label: 'Paramètres',
      href: '/settings',
      icon: <Settings className="w-4 h-4" />
    },
    {
      label: 'Statistiques',
      href: '/stats',
      icon: <BarChart className="w-4 h-4" />
    },
    {
      label: 'Se déconnecter',
      onClick: handleLogout,
      icon: <LogOut className="w-4 h-4" />,
      divider: true
    },
  ];

  // Language dropdown items
  const languageDropdownItems = [
    { label: 'Français', onClick: () => {} },
    { label: 'English', onClick: () => {} },
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  function handleRegisterClick() {
    navigate('/register');
  }

  function handleLoginClick() {
    navigate('/login');
  }

  function handleLogout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('userProfile');
    navigate('/');
  }

  function handleHomeClick() {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      navigate('/');
    }
  }

  return (
    <header
      className="sticky top-0 z-40 w-full"
      style={{
        background: '#212121',
        color: '#ECECEC',
        fontFamily: 'Arial, sans-serif',
        height: 60,
        minHeight: 60,
        maxHeight: 60,
      }}
    >
      <div
        className="w-full max-w-[1512px] mx-auto flex items-center justify-between box-border px-4 md:px-20 h-[60px]"
        style={{
          height: 60,
          margin: '0 auto',
          padding: undefined, // padding handled by Tailwind
          display: undefined, // handled by Tailwind
          alignItems: undefined, // handled by Tailwind
          justifyContent: undefined, // handled by Tailwind
          boxSizing: undefined, // handled by Tailwind
        }}
      >
        {/* Logo and Navigation */}
        <div className="flex items-center gap-4 md:gap-10">
          <div onClick={handleHomeClick}>
            <img src="/img/Logo.png" alt="Hi3D Logo" style={{ height: 28, width: 'auto', display: 'block', cursor: 'pointer', userSelect: 'none' }} />
          </div>
          {/* Desktop Navigation */}
          <nav className="hidden md:flex gap-4 md:gap-10 ml-4 md:ml-8">
            {navLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className="text-base font-medium transition-colors"
                style={{ color: '#ECECEC', fontFamily: 'Arial, sans-serif', fontWeight: 500, fontSize: 15 }}
              >
                {link.label}
              </a>
            ))}
          </nav>
        </div>
        {/* Right side: Auth buttons or User menu */}
        <div className="flex items-center gap-3 md:gap-3">
          {isAuthenticated ? (
            <>
              {/* Icons: Envelope, User, Favoris */}
              <button type="button" className="rounded-full hover:bg-neutral-800 mr-0" aria-label="Messages">
                <img src="/img/icone/envelope-f.png" alt="Messages" style={{ width: 24, height: 24, display: 'block' }} />
              </button>
              <button type="button" className="rounded-full hover:bg-neutral-800" aria-label="User">
                <img src="/img/icone/user.png" alt="User" style={{ width: 20, height: 20, display: 'block' }} />
              </button>
              <button type="button" className="rounded-full hover:bg-neutral-800" aria-label="Favoris" style={{ marginRight: 8 }}>
                <img src="/img/icone/favoris.png" alt="Favoris" style={{ width: 30, height: 30, display: 'block' }} />
              </button>
              {/* Go Pro Button */}
              <button
                type="button"
                style={{
                  color: '#212121',
                  background: '#fff',
                  border: 'none',
                  borderRadius: 9999,
                  width: 109,
                  height: 40,
                  padding: 0,
                  fontFamily: 'Arial, sans-serif',
                  fontWeight: 500,
                  fontSize: 16,
                  lineHeight: '20px',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                  marginLeft: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                Go Pro
              </button>
            </>
          ) : (
            <div className="hidden sm:flex items-center gap-4 md:gap-6">
              <button
                type="button"
                onClick={handleRegisterClick}
                style={{
                  color: '#ECECEC',
                  background: 'transparent',
                  border: 'none',
                  borderRadius: 9999,
                  padding: '8px 24px',
                  fontFamily: 'Arial, sans-serif',
                  fontWeight: 500,
                  fontSize: 15,
                  lineHeight: '20px',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                }}
              >
                Sign up
              </button>
              <button
                type="button"
                onClick={handleLoginClick}
                style={{
                  color: '#212121',
                  background: '#fff',
                  border: 'none',
                  borderRadius: 9999,
                  padding: '8px 24px',
                  fontFamily: 'Arial, sans-serif',
                  fontWeight: 500,
                  fontSize: 15,
                  lineHeight: '20px',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                }}
              >
                Log in
              </button>
            </div>
          )}
          {/* Mobile Menu Button */}
          <button
            type="button"
            className="md:hidden p-2 rounded-md hover:bg-neutral-800"
            onClick={() => setIsMobileMenuOpen(true)}
            aria-label="Open mobile menu"
            style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <Menu className="h-6 w-6 text-white" />
          </button>
        </div>
      </div>
      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        links={navLinks}
        authButtons={
          !isAuthenticated ? (
            <div className="flex flex-col space-y-3">
              <button
                type="button"
                onClick={handleLoginClick}
                className="w-full px-4 py-2 rounded-full bg-white text-neutral-900 font-medium hover:bg-neutral-200 transition-colors"
              >
                Log in
              </button>
              <button
                type="button"
                onClick={handleRegisterClick}
                className="w-full px-4 py-2 rounded-full border border-white text-white font-medium hover:bg-neutral-800 transition-colors"
                style={{ background: 'transparent' }}
              >
                Sign up
              </button>
            </div>
          ) : null
        }
      />
      {/* Profile Wizard Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-11/12 sm:w-3/4 max-w-lg relative">
            <button
              type="button"
              className="absolute top-3 right-3 text-neutral-500 hover:text-neutral-800"
              onClick={() => setIsModalOpen(false)}
              aria-label="Close"
            >
              ✖
            </button>
            <ProfileWizard />
          </div>
        </div>
      )}
    </header>
  );
}

export default Header;