import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Plus } from 'lucide-react';
import ServiceOffersList from './ServiceOffersList';
import ServiceOfferForm from './ServiceOfferForm';
import ServiceOfferDetails from './ServiceOfferDetails';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import DashboardLayout from '../dashboard/DashboardLayout';
import type { ServiceOffer, ServiceOfferFormData } from './types';
import { API_BASE_URL } from '../../config';

const ServiceOffersManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const [services, setServices] = useState<ServiceOffer[]>([]);
  const [selectedService, setSelectedService] = useState<ServiceOffer | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formLoading, setFormLoading] = useState<boolean>(false);
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');


  // Vérifier le statut professionnel de l'utilisateur avec plus de détails
  console.log("User object from localStorage:", user);
  console.log("User is_professional:", user.is_professional);
  console.log("User is_professional type:", typeof user.is_professional);

  // Vérifier si l'utilisateur est un professionnel (avec vérification plus robuste)
  const isProfessional = user && (
    user.is_professional === true ||
    user.is_professional === 1 ||
    user.is_professional === "1" ||
    user.is_professional === "true"
  );
  console.log("isProfessional:", isProfessional);

  const isEditMode = !!selectedService;

  // Récupérer le paramètre user de l'URL
  const searchParams = new URLSearchParams(location.search);
  const userIdParam = searchParams.get('user');

  // Rediriger les clients vers la page d'exploration des services
  useEffect(() => {
    // Désactiver temporairement la redirection pour déboguer
    console.log("Checking professional status:", isProfessional);

    // Ne pas rediriger si on est sur la page /dashboard/services avec un paramètre user
    if (!isProfessional && !(location.pathname === '/dashboard/services' && userIdParam)) {
      console.log("Not a professional, redirecting to /services");
      navigate('/services');
      return;
    }

    // Si l'URL contient un ID, c'est une page de détail ou d'édition
    const path = location.pathname;
    console.log("Current path:", path);
    if (id) {
      // Ne rien faire, le service sera chargé par l'effet qui dépend de l'ID
    } else if (path.includes('/dashboard/services/create')) {
      // Si c'est la page de création, afficher le formulaire
      console.log("Showing create form");
      setShowForm(true);
      setSelectedService(null);
    // } else if (path.includes('/dashboard/services/edit')) {
    //   // Si c'est la page de création, afficher le formulaire
    //   console.log("Showing create form");
    //   setShowForm(true);
    //   setSelectedService(null);
    } else {
      // Sinon, c'est la liste
      setShowForm(false);
      setSelectedService(null);
    }
  }, [isProfessional, navigate, location.pathname, id, userIdParam]);

  // Rediriger vers la page de détail si l'URL change
  useEffect(() => {
    if (id && !showForm) {
      // Si on a un ID dans l'URL et qu'on n'est pas en mode formulaire,
      // c'est une page de détail
      console.log("URL contains ID, showing details for service:", id);
      // Le service sera chargé par l'effet qui dépend de l'ID
    } else if (location.pathname === '/dashboard/services' && userIdParam) {
      // Si on est sur la page /dashboard/services avec un paramètre user,
      // c'est la liste des services d'un professionnel spécifique
      console.log("URL contains user parameter, showing services for user:", userIdParam);
      // Les services seront chargés par l'effet qui dépend de userIdParam
    } else if (location.pathname === '/dashboard/services') {
      // Si on est sur la page /dashboard/services sans paramètre,
      // c'est la liste des services du professionnel connecté
      console.log("URL is /dashboard/services, showing services for current user");
      // Les services seront chargés par l'effet qui dépend de l'ID de l'utilisateur
    } else if (location.pathname.includes('/dashboard/services/create')) {
      // Si on est sur la page /dashboard/services/create,
      // c'est la page de création d'un service
      console.log("URL is /dashboard/services/create, showing create form");
      setShowForm(true);
      setSelectedService(null);
    }
  }, [location.pathname, userIdParam, user.id]);

  // Charger les services au chargement du composant
  useEffect(() => {
    const fetchServices = async () => {
      setIsLoading(true);
      console.log("Chargement des services...");
      console.log("ID du service recherché:", id);

      try {
        // Si un ID est fourni, récupérer directement ce service spécifique
        if (id) {
          console.log("Récupération directe du service avec ID:", id);
          const serviceUrl = `${API_BASE_URL}/api/service-offers/${id}`;
          console.log("URL de l'API pour le service spécifique:", serviceUrl);

          const serviceResponse = await fetch(serviceUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!serviceResponse.ok) {
            console.error("Erreur lors de la récupération du service spécifique:", serviceResponse.status);
            throw new Error(`Erreur lors de la récupération du service (${serviceResponse.status})`);
          }

          const serviceData = await serviceResponse.json();
          console.log("Données du service récupéré:", serviceData);

          // Formater le service
          const formattedService = {
            id: serviceData.id,
            user_id: serviceData.user_id,
            title: serviceData.title,
            description: serviceData.description,
            price: serviceData.price,
            execution_time: serviceData.execution_time,
            concepts: serviceData.concepts,
            revisions: serviceData.revisions,
            categories: typeof serviceData.categories === 'string' ? JSON.parse(serviceData.categories) : (serviceData.categories || []),
            is_private: serviceData.is_private !== false,
            status: serviceData.status || 'published',
            created_at: serviceData.created_at,
            updated_at: serviceData.updated_at,
            user: serviceData.user ? {
              id: serviceData.user.id,
              first_name: serviceData.user.first_name,
              last_name: serviceData.user.last_name,
              profile_picture_path: serviceData.user.profile_picture_path,
            } : undefined,
            files: serviceData.files || [],
            imageUrl: serviceData.files && serviceData.files.length > 0
              ? `${API_BASE_URL}/storage/${serviceData.files[0].path}`
              : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            file_urls: Array.isArray(serviceData.files)
            ? serviceData.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
            likes: serviceData.likes || 0,
            views: serviceData.views || 0,
          };

          console.log("Service formaté:", formattedService);
          setSelectedService(formattedService);

          // Récupérer également la liste des services pour l'affichage en arrière-plan
          fetchServicesList();
        } else {
          // Sinon, récupérer la liste des services
          fetchServicesList();
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les services');
      } finally {
        setIsLoading(false);
      }
    };

    // Fonction pour récupérer la liste des services
    const fetchServicesList = async () => {
      try {
        // Si un userIdParam est fourni dans l'URL, l'utiliser
        // Sinon, utiliser l'ID de l'utilisateur connecté
        const userId = userIdParam || user.id;

        // Utiliser l'API dédiée pour récupérer les services d'un professionnel spécifique
        const url = `${API_BASE_URL}/api/professionals/${userId}/service-offers`;

        console.log("URL de l'API pour la liste des services:", url, "User ID:", userId);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          console.error("Erreur lors de la récupération des services:", response.status);
          throw new Error(`Erreur lors de la récupération des services (${response.status})`);
        }

        const data = await response.json();
        console.log("Réponse API complète:", data);
        console.log("Type de la réponse API:", typeof data);

        // Vérifier si data est un tableau ou un objet avec une propriété data
        let apiServices = [];

        // Vérifier si data est un tableau
        if (Array.isArray(data)) {
          console.log("La réponse API est un tableau");
          apiServices = data;
        }
        // Vérifier si data est un objet avec une propriété data
        else if (data && typeof data === 'object') {
          console.log("La réponse API est un objet");

          // Vérifier si data.data existe et est un tableau
          if (data.data && Array.isArray(data.data)) {
            console.log("La réponse API contient une propriété data qui est un tableau");
            apiServices = data.data;
          }
          // Sinon, essayer d'utiliser data directement
          else {
            console.log("Utilisation de data directement");
            apiServices = [data];
          }
        }

        console.log("Services bruts de l'API:", apiServices.length);

        // Vérifier la structure des services
        if (apiServices.length > 0) {
          console.log("Structure du premier service:", JSON.stringify(apiServices[0], null, 2));
        }

        console.log("Services pour l'utilisateur", userId, ":", apiServices.length);

        // Transformer les données de l'API
        const formattedServices = apiServices.map((service: any) => ({
          id: service.id,
          user_id: service.user_id,
          title: service.title,
          description: service.description,
          price: service.price,
          execution_time: service.execution_time,
          concepts: service.concepts,
          revisions: service.revisions,
          categories: typeof service.categories === 'string' ? JSON.parse(service.categories) : (service.categories || []),
          is_private: service.is_private !== false,
          status: service.status || 'published',
          created_at: service.created_at,
          updated_at: service.updated_at,
          user: service.user ? {
            id: service.user.id,
            first_name: service.user.first_name,
            last_name: service.user.last_name,
            profile_picture_path: service.user.profile_picture_path,
          } : undefined,
          files: service.files || [],
          imageUrl: service.files && service.files.length > 0
            ? `${API_BASE_URL}/storage/${service.files[0].path}`
            : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          file_urls: Array.isArray(service.files)
            ? service.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
          likes: service.likes || 0,
          views: service.views || 0,
        }));

        setServices(formattedServices);

        console.log("Services récupérés:", formattedServices.length);
      } catch (err) {
        console.error('Erreur lors de la récupération de la liste des services:', err);
        setError('Impossible de charger la liste des services');
      }
    };

    fetchServices();
  }, [token, id, userIdParam, user.id]);

  // Gérer la création d'un nouveau service
  const handleCreateService = () => {
    setSelectedService(null);
    setShowForm(true);
  };

  // Gérer la modification d'un service existant
  const handleEditService = (service: ServiceOffer) => {
    setSelectedService(service);
    setShowForm(true);
  };

  // Gérer la suppression d'un service
  const handleDeleteService = async (serviceId: number) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce service ?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/service-offers/${serviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du service');
      }

      // Mettre à jour la liste des services
      setServices(services.filter(service => service.id !== serviceId));

      // Si le service supprimé était sélectionné, désélectionner
      if (selectedService && selectedService.id === serviceId) {
        setSelectedService(null);
        // Rediriger vers la page des services du professionnel connecté avec le paramètre user
        // Utiliser window.location.href pour forcer un rechargement complet
        const timestamp = new Date().getTime();
        window.location.href = `/dashboard/services?user=${user.id}&t=${timestamp}`;
      }

      setFormSuccess('Service supprimé avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      setFormError('Impossible de supprimer le service');
    }
  };

  const handleFormSubmit = async (formData: ServiceOfferFormData) => {
    setFormLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      const payload = {
        title: formData.title,
        description: formData.description,
        price: typeof formData.price === 'string' ? parseFloat(formData.price) : formData.price,
        execution_time: formData.execution_time,
        concepts: formData.concepts,
        revisions: formData.revisions,
        categories: formData.categories,
        is_private: formData.is_private,
        status: formData.status,
      };

      let requestBody;
      let headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
      };

      if (formData.images && formData.images.length > 0) {
        const formDataObj = new FormData();

        formData.images.forEach((file: File) => {
          formDataObj.append('files[]', file);
        });

        Object.entries(payload).forEach(([key, value]) => {
          if (key === 'categories') {
            // Envoyer chaque catégorie individuellement
            if (Array.isArray(value)) {
              value.forEach((category, index) => {
                formDataObj.append(`categories[${index}]`, category);
              });
            }
          } else if (key === 'is_private') {
            formDataObj.append(key, value ? '1' : '0');
          } else {
            formDataObj.append(key, String(value));
          }
        });

        requestBody = formDataObj;
      } else {
        // Pour les requêtes sans fichiers, s'assurer que les catégories sont bien un tableau
        if (Array.isArray(payload.categories)) {
          payload.categories = payload.categories;
        } else if (typeof payload.categories === 'string') {
          payload.categories = [payload.categories];
        } else {
          payload.categories = [];
        }
        requestBody = JSON.stringify(payload);
        headers['Content-Type'] = 'application/json';
      }

      const endpoint = isEditMode
        ? `${API_BASE_URL}/api/service-offers/${selectedService?.id}`
        : `${API_BASE_URL}/api/service-offers`;

      const response = await fetch(endpoint, {
        method: isEditMode ? 'POST' : 'POST',
        headers,
        body: requestBody,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Erreur lors de ${isEditMode ? 'la modification' : 'la création'} du service`);
      }

      const data = await response.json();
      
      if (isEditMode) {
        setServices(services.map(service =>
          service.id === selectedService?.id ? {
            ...service,
            ...payload,
            updated_at: new Date().toISOString()
          } : service
        ));
        setFormSuccess('Service mis à jour avec succès');
        // Forcer l'actualisation de la page avec window.location.href
        window.location.href = `/dashboard/services?user=${user.id}`;
      } else {
        const newService: ServiceOffer = {
          ...payload,
          id: data.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user: {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            profile_picture_path: user.profile_picture_path,
          },
          files: data.files || [],
          imageUrl: data.files && data.files.length > 0
            ? `${API_BASE_URL}/storage/${data.files[0].path}`
            : undefined,
          file_urls: Array.isArray(data.files)
            ? data.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
          likes: 0,
          views: 0,
        };
        setServices([...services, newService]);
        setFormSuccess('Service créé avec succès');
        // Rediriger vers la page des services avec le paramètre user
        navigate(`/dashboard/services?user=${user.id}`);
      }

      setShowForm(false);
    } catch (err) {
      console.error('Erreur:', err);
      setFormError(err instanceof Error ? err.message : `Impossible de ${isEditMode ? 'modifier' : 'créer'} le service`);
    } finally {
      setFormLoading(false);
    }
  };

  // Gérer l'annulation du formulaire
  const handleFormCancel = () => {
    setShowForm(false);
    if (!isEditMode) {
      setSelectedService(null);
    }
  };

  // Gérer le clic sur un service
  const handleServiceClick = (serviceId: number) => {
    navigate(`/dashboard/services/${serviceId}`);
  };

  const handleSelectService = (service: ServiceOffer) => {
    setSelectedService(service);
  }

  // Gérer le partage d'un service
  const handleShareService = (serviceId: number) => {
    const url = `${window.location.origin}/service/${serviceId}`;
    navigator.clipboard.writeText(url);
    alert('Lien copié dans le presse-papier');
  };

  return (
    <DashboardLayout
      title={showForm ? (isEditMode ? "Modifier mon service" : "Créer un nouveau service") : "Mes services professionnels"}
      subtitle={showForm ? "Remplissez les détails de votre service" : "Gérez vos services pour les proposer aux clients"}
      actions={
        !showForm && !selectedService && isProfessional ? (
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateService}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            Créer un service
          </Button>
        ) : null
      }
    >
      {/* Afficher les erreurs */}
      {(error || formError) && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => {
            setError(null);
            setFormError(null);
          }}
          className="mb-6"
        >
          {error || formError}
        </Alert>
      )}

      {/* Afficher les succès */}
      {formSuccess && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setFormSuccess(null)}
          className="mb-6"
        >
          {formSuccess}
        </Alert>
      )}

      {/* Afficher le formulaire, les détails ou la liste */}
      {showForm ? (
        <ServiceOfferForm
          initialData={isEditMode ? selectedService : undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={formLoading}
        />
      ) : selectedService ? (
        <ServiceOfferDetails
          service={selectedService}
          onEdit={handleEditService}
          onDelete={handleDeleteService}
          onShare={handleShareService}
        />
      ) : (
        <ServiceOffersList
          services={services} // Utiliser tous les services récupérés depuis l'API
          isLoading={isLoading}
          error={error || undefined}
          onServiceClick={handleServiceClick}
          onCreateService={handleCreateService}
          onSelectService={handleSelectService}
          emptyMessage="Vous n'avez pas encore créé de service"
        />
      )}
    </DashboardLayout>
  );
};

export default ServiceOffersManagementPage;
