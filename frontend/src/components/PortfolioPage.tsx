import React from 'react';
import CategoryList from './CategoryList';
import Gallery from './Gallery';

const PortfolioPage: React.FC = () => {
  // Catégories statiques
  const categories = [
    'Modélisation 3D', 'Animation 3D', 'Rendu', 'Texturing', 'Rigging', 'Compositing', 'VFX', 'Architecture 3D', 'Jeux Vidéo'
  ];

  // Données de galerie statiques (exemple)
  const galleryItems = [
    { 
      id: 1, 
      title: 'SIRIUS - Space Station', 
      author: '<PERSON>', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/44.jpg', 
      isPro: true, 
      likes: 35, 
      views: '3.3k', 
      image: 'https://cdnb.artstation.com/p/assets/covers/images/089/146/321/smaller_square/samuel-navarro-samuel-navarro-32.jpg?1750169043'
    },
    { 
      id: 2, 
      title: 'Purple Lake Environment', 
      author: '<PERSON><PERSON>', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/32.jpg', 
      isPro: false, 
      likes: 83, 
      views: '18.9k', 
      image: 'https://cdnb.artstation.com/p/assets/covers/images/067/372/527/smaller_square/dmitry-fedorov-dmitry-fedorov-icon-new.jpg?1695215260'
    },
    { 
      id: 3, 
      title: 'Cyberpunk Cityscape', 
      author: 'Maria Rodriguez', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/28.jpg', 
      isPro: true, 
      likes: 127, 
      views: '45.2k', 
      image: 'https://cdna.artstation.com/p/assets/covers/images/059/474/796/20230215081009/smaller_square/flycat-fly-flycat-fly-render-5g.jpg?1676470209'
    },
    { 
      id: 4, 
      title: 'Fantasy Character Design', 
      author: 'Alex Chen', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/67.jpg', 
      isPro: true, 
      likes: 94, 
      views: '22.1k', 
      image: 'https://cdna.artstation.com/p/assets/covers/images/074/103/832/smaller_square/francois-larrieu-francois-larrieu-thumbnail.jpg?1711233634'
    },
    { 
      id: 5, 
      title: 'Modern Architecture Villa', 
      author: 'Sophie Dubois', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/55.jpg', 
      isPro: false, 
      likes: 156, 
      views: '67.8k', 
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop'
    },
    { 
      id: 6, 
      title: 'Sci-Fi Robot Animation', 
      author: 'David Kim', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/89.jpg', 
      isPro: true, 
      likes: 203, 
      views: '89.4k', 
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop'
    },
    { 
      id: 7, 
      title: 'Nature Landscape Render', 
      author: 'Emma Wilson', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/33.jpg', 
      isPro: false, 
      likes: 67, 
      views: '15.6k', 
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
    },
    { 
      id: 8, 
      title: 'Game Character Rigging', 
      author: 'Lucas Santos', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/45.jpg', 
      isPro: true, 
      likes: 178, 
      views: '52.3k', 
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
    },
    { 
      id: 9, 
      title: 'Product Visualization', 
      author: 'Anna Kowalski', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/71.jpg', 
      isPro: false, 
      likes: 89, 
      views: '28.7k', 
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
    },
    { 
      id: 10, 
      title: 'VFX Explosion Sequence', 
      author: 'Thomas Müller', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/23.jpg', 
      isPro: true, 
      likes: 245, 
      views: '112.9k', 
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop'
    },
    { 
      id: 11, 
      title: 'Interior Design Scene', 
      author: 'Isabella Garcia', 
      authorAvatar: 'https://randomuser.me/api/portraits/women/62.jpg', 
      isPro: true, 
      likes: 134, 
      views: '41.2k', 
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop'
    },
    { 
      id: 12, 
      title: 'Character Animation Test', 
      author: 'Michael Johnson', 
      authorAvatar: 'https://randomuser.me/api/portraits/men/18.jpg', 
      isPro: false, 
      likes: 76, 
      views: '19.8k', 
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
    }
  ];

  return (
    <div style={{ marginTop: '60px' }}>
      <CategoryList categories={categories} />
      <Gallery items={galleryItems} />
    </div>
  );
};

export default PortfolioPage; 