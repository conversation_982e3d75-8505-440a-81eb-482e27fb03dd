import React, { useState } from 'react';
import Header from './Header';
import Footer from './Footer';
import MyWork from './MyWork';

// Composant inspiré de CivilityState pour l'entête "My Portfolio"
const PortfolioHeader: React.FC = () => {
  return (
    <div className="mt-10 mb-6">
      <h1 style={{ fontSize: 40, fontWeight: 700, color: '#2D2D2D', marginBottom: 24, fontFamily: 'Arial, sans-serif' }}>
        My Portfolio
      </h1>
      <button
        style={{
          border: '1px solid #E5E5E5',
          borderRadius: 24,
          padding: '12px 24px',
          background: '#fff',
          color: '#222',
          fontWeight: 500,
          fontSize: 16,
          fontFamily: 'Arial, sans-serif',
          cursor: 'pointer',
          boxShadow: 'none',
        }}
      >
        View my portfolio page
      </button>
    </div>
  );
};

// Composant inspiré de ProjectTabs pour les onglets
const ProfileTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'work' | 'offers' | 'about'>('work');

  return (
    <div className="mb-8">
      <div style={{ display: 'flex', gap: 32, borderBottom: '1.5px solid #222', width: '100%', maxWidth: 1200 }}>
        <button
          onClick={() => setActiveTab('work')}
          style={{
            background: 'none',
            border: 'none',
            borderBottom: activeTab === 'work' ? '2.5px solid #222' : 'none',
            color: activeTab === 'work' ? '#222' : '#888',
            fontWeight: activeTab === 'work' ? 600 : 400,
            fontSize: 15,
            padding: '12px 0',
            cursor: 'pointer',
            outline: 'none',
            transition: 'color 0.2s',
          }}
        >
          My work
        </button>
        <button
          onClick={() => setActiveTab('offers')}
          style={{
            background: 'none',
            border: 'none',
            borderBottom: activeTab === 'offers' ? '2.5px solid #222' : 'none',
            color: activeTab === 'offers' ? '#222' : '#888',
            fontWeight: activeTab === 'offers' ? 600 : 400,
            fontSize: 15,
            padding: '12px 0',
            cursor: 'pointer',
            outline: 'none',
            transition: 'color 0.2s',
          }}
        >
          My offers
        </button>
        <button
          onClick={() => setActiveTab('about')}
          style={{
            background: 'none',
            border: 'none',
            borderBottom: activeTab === 'about' ? '2.5px solid #222' : 'none',
            color: activeTab === 'about' ? '#222' : '#888',
            fontWeight: activeTab === 'about' ? 600 : 400,
            fontSize: 15,
            padding: '12px 0',
            cursor: 'pointer',
            outline: 'none',
            transition: 'color 0.2s',
          }}
        >
          About me
        </button>
      </div>
      {/* Optionally, you can add tab content here if needed */}
    </div>
  );
};

// Définir les données ici :
const galleryItems = [
  { 
    id: 1, 
    title: 'SIRIUS - Space Station', 
    author: 'Helen Jhones', 
    authorAvatar: 'https://randomuser.me/api/portraits/women/44.jpg', 
    isPro: true, 
    likes: 35, 
    views: '3.3k', 
    image: 'https://images.unsplash.com/photo-1446776811953-b23d57bd21aa?w=800&h=600&fit=crop'
  },
  { 
    id: 2, 
    title: 'Purple Lake Environment', 
    author: 'Andrey Prokopenko', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/32.jpg', 
    isPro: false, 
    likes: 83, 
    views: '18.9k', 
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
  },
  { 
    id: 3, 
    title: 'Your Health - UI', 
    author: 'Ronas IT | UI/UX Team', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/45.jpg', 
    isPro: false, 
    likes: 59, 
    views: '3.9k', 
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
  },
  { 
    id: 4, 
    title: 'Oxford AR', 
    author: 'Plainthing Studio', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/23.jpg', 
    isPro: false, 
    likes: 63, 
    views: '6.3k', 
    image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop'
  }
];

const portfolioContainerStyle = {
  maxWidth: 1400,
  margin: '0 auto',
  paddingLeft: 16,
  paddingRight: 16,
};

const EditComponent: React.FC = () => {
  return (
    <div style={{ background: '#fff', minHeight: '100vh' }}>
      <Header />
      <main style={{ minHeight: '70vh' }}>
        <div className="px-4 md:px-20 w-full max-w-[1512px] mx-auto">
          <PortfolioHeader />
          <ProfileTabs />
          <div style={{ marginTop: 45, marginBottom: 450 }}>
            <MyWork items={galleryItems} />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default EditComponent; 