import React from 'react';

// interface Project {
//   title: string;
//   author: string;
//   avatar: string;
//   description: string;
//   budget: string;
//   period: string;
//   status: 'open' | 'in_progress' | 'completed';
// }


interface ProjectCardsProps {
  projects: any[];
  activeTab: 'open' | 'ongoing' | 'completed';
}

const ProjectCards: React.FC<ProjectCardsProps> = ({ projects, activeTab }) => {
  const getStatusFilter = () => {
    switch (activeTab) {
      case 'open':
        return 'open';
      case 'ongoing':
        return 'in_progress';
      case 'completed':
        return 'completed';
      default:
        return 'open';
    }
  };

  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return '<PERSON><PERSON>lai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  const filteredProjects = projects.filter(p => p.status === getStatusFilter());

  return (
    <div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mx-auto mt-12"
      style={{ maxWidth: '1500px' }}
    >
      {filteredProjects.length === 0 ? (
        <div className="col-span-full text-center text-gray-500 font-semibold text-md">
          Aucun projet à afficher pour cette catégorie.
        </div>
      ) : (
        filteredProjects.map((project, idx) => (
          <div key={idx}
            className="rounded-2xl max-w-[350px] h-[270px] w-full p-0"
            style={{ boxShadow: '0 0 0 8px #f6f8fe' }}
          >
            <div className="bg-white rounded-2xl p-3 h-full flex flex-col max-w-[350px] w-full">
              <div className="rounded-xl bg-white px-0 py-3 border border-gray-200 h-full flex flex-col overflow-hidden">
                <div className="mb-2 px-2">
                  <h3 className="text-sm font-bold text-gray-800 mb-0.5 line-clamp-1 py-0.5">
                    {project.title}
                  </h3>
                  <div className="flex items-center gap-1 mb-1">
                    <img src={project.client.avatar} alt={project.client.name} className="w-4 h-4 rounded-full object-cover border border-gray-200" />
                    <span className="text-xs font-medium text-gray-700">{project.client.name}</span>
                  </div>
                </div>
                <div className="border-t border-gray-200 mb-2"></div>
                <div className="mb-2 flex-grow overflow-hidden px-2">
                  <span className="block text-xs font-semibold text-gray-400 tracking-widest py-2">DESCRIPTION</span>
                  <div className="text-gray-800 whitespace-pre-line text-xs leading-tight line-clamp-3 font-semibold mb-1">{project.description}</div>
                </div>
                <div className="flex border-t py-0 gap-2 px-2">
                  <div className='px-2 border-r py-2 border-gray-200'>
                    <span className="block text-xs font-semibold text-gray-400 tracking-widest mb-1">BUDGET</span>
                    <div>
                      <span className="text-xs align-top text-gray-500 mr-1">US$</span>
                      <span className="text-xs font-semibold text-gray-800">{project.budget.replace('US$', '')}</span>
                    </div>
                  </div>
                  <div className='py-2'>
                    <span className="block text-xs font-semibold text-gray-400 tracking-widest mb-1">PÉRIODE</span>
                    <div className="text-xs text-gray-700 font-semibold mt-2">{getDaysRemaining(project.deadline)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ProjectCards;

// import React from 'react';

// const mockProjects = [
//     {
//         title: '3d visualisation interior villa',
//         author: 'Wassim Honeiny',
//         avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
//         description: 'Hello,\nI would like to do a 3d image of a villa i would like 3 render. One of the bedroom and 2 rooms. could you please tell me what is you pricing ?\n\nThank you',
//         budget: 'US$500-1000',
//         period: 'Dans un mois'
//     }, {
//         title: '3d visualisation interior villa',
//         author: 'Wassim Honeiny',
//         avatar: 'https://randomuser.me/api/portraits/men/33.jpg',
//         description: 'Hello,\nI would like to do a 3d image of a villa i would like 3 render. One of the bedroom and 2 rooms. could you please tell me what is you pricing ?\n\nThank you',
//         budget: 'US$500-1000',
//         period: 'Dans un mois'
//     }, {
//         title: '3d visualisation interior villa',
//         author: 'Wassim Honeiny',
//         avatar: 'https://randomuser.me/api/portraits/men/34.jpg',
//         description: 'Hello,\nI would like to do a 3d image of a villa i would like 3 render. One of the bedroom and 2 rooms. could you please tell me what is you pricing ?\n\nThank you',
//         budget: 'US$500-1000',
//         period: 'Dans un mois'
//     }, {
//         title: '3d visualisation interior villa',
//         author: 'Wassim Honeiny',
//         avatar: 'https://randomuser.me/api/portraits/men/35.jpg',
//         description: 'Hello,\nI would like to do a 3d image of a villa i would like 3 render. One of the bedroom and 2 rooms. could you please tell me what is you pricing ?\n\nThank you',
//         budget: 'US$500-1000',
//         period: 'Dans un mois'
//     },
// ];

// const ProjectCards: React.FC = ({  }) => {
//     return (
//         <div
//             className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mx-auto mt-12"
//             style={{ maxWidth: '1500px' }}
//         >
//             {mockProjects.map((project, idx) => (
//                 <div key={idx}
//                     className="rounded-2xl max-w-[350px] h-[270px] w-full p-0"
//                     style={{ boxShadow: '0 0 0 8px #f6f8fe' }}
//                 >
//                     <div className="bg-white rounded-2xl p-3 h-full flex flex-col max-w-[350px] w-full">
//                         <div className="rounded-xl bg-white px-0 py-3 border border-gray-200 h-full flex flex-col overflow-hidden">
//                             <div className="mb-2 px-2">
//                                 <h3 className="text-sm font-bold text-gray-800 mb-0.5 line-clamp-1 py-0.5">
//                                     {project.title}
//                                 </h3>
//                                 <div className="flex items-center gap-1 mb-1">
//                                     <img src={project.avatar} alt={project.author} className="w-4 h-4 rounded-full object-cover border border-gray-200" />
//                                     <span className="text-xs font-medium text-gray-700">{project.author}</span>
//                                 </div>
//                             </div>
//                             <div className="border-t border-gray-200 mb-2"></div>
//                             <div className="mb-2 flex-grow overflow-hidden px-2">
//                                 <span className="block text-xs font-semibold text-gray-400 tracking-widest py-2">DESCRIPTION</span>
//                                 <div className="text-gray-800 whitespace-pre-line text-xs leading-tight line-clamp-3 font-semibold mb-1">{project.description}</div>
//                             </div>
//                             <div className="flex border-t py-0 gap-2 px-2">
//                                 <div className='px-2 border-r py-2 border-gray-200'>
//                                     <span className="block text-xs font-semibold text-gray-400 tracking-widest mb-1">BUDGET</span>
//                                     <div>
//                                         <span className="text-xs align-top text-gray-500 mr-1">US$</span>
//                                         <span className="text-xs font-semibold text-gray-800">{project.budget.replace('US$', '')}</span>
//                                     </div>
//                                 </div>
//                                 <div className='py-2'>
//                                     <span className="block text-xs font-semibold text-gray-400 tracking-widest mb-1">PÉRIODE</span>
//                                     <div className="text-xs text-gray-700 font-semibold mt-2">{project.period}</div>
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 </div>
//             ))}
//         </div>
//     );
// };

// export default ProjectCards;
