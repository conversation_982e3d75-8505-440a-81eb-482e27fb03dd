<?php

namespace Database\Factories;

use App\Models\Achievement;
use App\Models\ProfessionalProfile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Achievement>
 */
class AchievementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Achievement::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'professional_profile_id' => ProfessionalProfile::factory(),
            'title' => $this->faker->sentence(4),
            'organization' => $this->faker->company(),
            'date_obtained' => $this->faker->dateTimeBetween('-5 years', 'now')->format('Y-m-d'),
            'description' => $this->faker->paragraphs(2, true),
            'file_path' => null, // Pas de fichier par défaut
            'files' => null, // Pas de fichiers par défaut dans les tests
            'achievement_url' => $this->faker->optional(0.6)->url(), // 60% chance d'avoir une URL
        ];
    }

    /**
     * Indicate that the achievement has a certificate URL.
     */
    public function withUrl(): static
    {
        return $this->state(fn (array $attributes) => [
            'achievement_url' => $this->faker->url(),
        ]);
    }

    /**
     * Indicate that the achievement is recent (within last year).
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'date_obtained' => $this->faker->dateTimeBetween('-1 year', 'now')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the achievement is for a specific type of project.
     */
    public function projectType(string $type): static
    {
        $titles = [
            'architecture' => [
                'Villa Moderne - Projet Résidentiel',
                'Immeuble de Bureaux - Centre Ville',
                'Rénovation Patrimoine Historique',
                'Complexe Commercial - Zone Urbaine'
            ],
            'interior' => [
                'Aménagement Appartement Haussmannien',
                'Design Restaurant Gastronomique',
                'Bureaux Open Space - Startup Tech',
                'Showroom Automobile Premium'
            ],
            'landscape' => [
                'Parc Urbain - Aménagement Paysager',
                'Jardin Privé - Villa de Luxe',
                'Espace Vert Entreprise',
                'Place Publique - Réaménagement'
            ]
        ];

        return $this->state(fn (array $attributes) => [
            'title' => $this->faker->randomElement($titles[$type] ?? $titles['architecture']),
        ]);
    }
}
